"use client";

import NotificationToast from "@/components/Notification";
import { createContext, useContext, useState, ReactNode } from "react";

type NotifType = "success" | "failure" | "info" | "warn";

interface Notification {
    renderObj: {
        leftSection?: ReactNode;
        midSection: ReactNode;
        rightSection?: ReactNode;
    }
    type: NotifType,
    duration?: number,
}

interface NotificationContextType {
    // notification: Notification | undefined;
    showNotification: (notification: Notification) => void | undefined;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider = ({ children }: { children: ReactNode }) => {
    const [notification, setNotification] = useState<Notification>();
    const [isOpen, setIsOpen] = useState<boolean>(false);

    const showNotification = (notification: Notification) => {
        setNotification(notification);
        setIsOpen(true)

        // Auto-remove notification after 4 seconds
        setTimeout(() => {
            setIsOpen(false);
        }, notification?.duration || 4000);
    };

    return (
        <NotificationContext.Provider value={{ showNotification }}>
            {children}

            {/* Notification Container */}
            {notification &&
                <NotificationToast
                    isOpen={isOpen}
                    type={notification.type}
                    renderObj={notification.renderObj}
                    duration={notification.duration}
                />}
        </NotificationContext.Provider>
    );
};

export const useNotification = () => {
    const context = useContext(NotificationContext);
    if (!context) {
        throw new Error("useNotification must be used within a NotificationProvider");
    }
    return context;
};
