"use client";

import AppTextArea from "@/components/AppTextArea";
import AppTextInput from "@/components/AppTextInput";
import Modal from "@/components/Modal";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { HiLocationMarker } from 'react-icons/hi';
import colors from "../../../../themePalette";
import ContactNav from "@/components/ContactNav";
import BareTextInput from "@/components/BareTextInput";
import SideLabelTextInput from "@/components/SideLabelTextInput";
import AppToggleSwitch from "@/components/AppToggleSwitch";
import { FaImage } from "react-icons/fa";
import { CgChevronLeft, CgChevronRight, CgClose } from "react-icons/cg";
import { BiChevronLeftCircle, BiChevronRightCircle } from "react-icons/bi";
import { useAuthStore } from "@/store/authStore";
import { useMutation } from "@tanstack/react-query";
import client from "@/api/client";
import { useNotification } from "@/contexts/NotificationProvider";
import { getBearerToken, getUserId } from "@/app/tokenStorage";
import { MdAccountCircle } from "react-icons/md";
import AppButton from "@/components/AppButton";
import { BiTrash } from "react-icons/bi";
import ImageCropper from "@/components/ImageCropper";

const coverPhotoUrls = [
    "/contact-bg.svg",
    "/cover-bgs/1.png",
    "/cover-bgs/2.png",
    "/cover-bgs/3.png",
    "/cover-bgs/4.png",
    "/cover-bgs/5.png",
    "/cover-bgs/6.png",
    "/cover-bgs/7.png",
    "/cover-bgs/8.png",
    "/cover-bgs/9.png",
    "/cover-bgs/10.png",
];


export default function LandingPageSettings() {
    const { user, setUser } = useAuthStore()
    const router = useRouter();
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [selectedCoverPhoto, setSelectedCoverPhoto] = useState(user?.coverPhoto || coverPhotoUrls[0])
    const [errorMessage, setErrorMessage] = useState("")
    const [enableSocialProofs, setEnableSocialProofs] = useState(false)
    const [formValues, setFormValues] = useState(user)
    const [showImageCropper, setShowImageCropper] = useState(false)
    const [selectedImageForCrop, setSelectedImageForCrop] = useState<string>("")
    const [whatsappError, setWhatsappError] = useState("")

    const { showNotification } = useNotification()

    const fileInputRef = useRef<HTMLInputElement | null>(null); // Create ref for the file input

    // WhatsApp validation function
    const validateWhatsAppNumber = (phoneNumber: string): string => {
        if (!phoneNumber.trim()) {
            return ""; // Empty is allowed
        }

        // Remove wa.me/ prefix if present for validation
        const cleanNumber = phoneNumber.replace(/^wa\.me\//, '').trim();

        if (cleanNumber && !cleanNumber.startsWith('+')) {
            return "WhatsApp number must include country code (e.g., +1234567890)";
        }

        return "";
    };

    const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setSelectedImageForCrop(reader.result as string);
                setShowImageCropper(true);
            };
            reader.readAsDataURL(file);
        }
        // Reset the input value so the same file can be selected again
        event.target.value = '';
    };

    const handleCropComplete = (croppedImageUrl: string) => {
        setFormValues((prev: any) => ({
            ...prev,
            profileImage: croppedImageUrl
        }));
        setShowImageCropper(false);
        setSelectedImageForCrop("");
    };

    const handleCropCancel = () => {
        setShowImageCropper(false);
        setSelectedImageForCrop("");
    };

    const handleRemoveImage = () => {
        setFormValues((prev: any) => ({
            ...prev,
            profileImage: ""
        }));
    };

    const triggerFileInput = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click(); // Trigger the file input click programmatically using the ref
        }
    };

    const updateProfile = useMutation({
        mutationFn: async (values: any) => {
            setErrorMessage("");
            const { emailAddress, ...formDataToSubmit } = values
            console.log("Values: ", formDataToSubmit)
            return client.post("/profile/update", formDataToSubmit, {
                headers: {
                    Authorization: getBearerToken()
                }
            });
        },
        onSuccess: async (response) => {
            console.log("response: ", response?.data?.data)
            setUser(response?.data?.data)
            showNotification({ renderObj: { midSection: "Profile update successful" }, type: "success" })
            router.push("/landing-page")
        },
        onError: (error: any) => {
            console.error("Profile update failed:", error);
            showNotification({ renderObj: { midSection: "Profile update failed" }, type: "failure" })
            setErrorMessage(
                error?.response?.data?.message || error?.message || "Profile update failed. Please try again."
            );
        },
    });

    useEffect(() => {
        setFormValues(user)
    }, [user])


    return (
        <div className="flex relative flex-col max-w-[55.81rem] mt-[1rem] md:mt-[7.81rem] pb-[5rem] md:pb-[11.06rem] mx-[0.94rem] md:mx-[5rem] lg:mx-[12rem] xl:mx-[17.63rem]">
            {/* Image Cropper Modal */}
            {showImageCropper && selectedImageForCrop && (
                <ImageCropper
                    imageSrc={selectedImageForCrop}
                    onCropComplete={handleCropComplete}
                    onCancel={handleCropCancel}
                    aspectRatio={1}
                    circularCrop={true}
                />
            )}

            <Modal isOpen={isModalOpen}>
                <div className="flex flex-col justify-center px-[1rem] items-center w-screen h-screen bg-black/50">
                    <div className="flex flex-col w-full max-w-[50rem] bg-white rounded-[0.625rem] pb-[2rem]">
                        <CgClose onClick={() => { setIsModalOpen(false) }} className="ml-auto mr-5 mt-5 cursor-pointer" size={"1.25rem"} color="black" />
                        <div className="font-semibold text-[1.25rem] mb-5 mx-[1rem] md:mx-[2rem]">Select a cover photo</div>
                        <div className="flex gap-[1rem] items-center mx-[1rem] md:mx-[2rem]">
                            <CgChevronLeft
                                className="cursor-pointer"
                                onClick={() => {
                                    const currentIndex = coverPhotoUrls.indexOf(selectedCoverPhoto);
                                    const prevIndex =
                                        currentIndex === 0 ? coverPhotoUrls.length - 1 : currentIndex - 1;
                                    setSelectedCoverPhoto(coverPhotoUrls[prevIndex]);
                                    console.log("Switching to cover photo:", coverPhotoUrls[prevIndex]);

                                }}
                                size={"2rem"}
                            />
                            <div className="flex-1">
                                <img
                                    src={selectedCoverPhoto}
                                    className="flex-1 shadow-lg h-[10rem] w-full border border-gray-300 object-cover rounded-[0.625rem]"
                                />
                            </div>
                            <CgChevronRight
                                className="cursor-pointer"
                                onClick={() => {
                                    const currentIndex = coverPhotoUrls.indexOf(selectedCoverPhoto);
                                    const nextIndex = (currentIndex + 1) % coverPhotoUrls.length;
                                    setSelectedCoverPhoto(coverPhotoUrls[nextIndex]);
                                    console.log("Switching to cover photo:", coverPhotoUrls[nextIndex]);

                                }}
                                size={"2rem"}
                            />
                        </div>

                        <div className="flex mt-5 justify-center">
                            <AppButton onClick={() => { setFormValues({ ...formValues, coverPhoto: selectedCoverPhoto }); setIsModalOpen(false) }} widthClass="px-[2rem]" text="Select" />
                        </div>
                    </div>
                </div>
            </Modal>

            <div className="font-semibold text-headertext text-[1.25rem]">Profile settings</div>
            <div className="font-semibold text-headertext mt-[3.25rem] md:text-[1.125rem]">Basic information</div>
            <div className="flex flex-col gap-[1.81rem]">
                <div className="h-[6.63rem] md:h-[5rem] w-full mt-[2.19rem] flex items-end border-t border-outline">
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                            First name
                        </div>
                        <div className="flex-1">
                            <BareTextInput
                                placeholder="Enter name"
                                type="text"
                                value={formValues?.firstName}
                                onChange={(e) =>
                                    setFormValues({ ...formValues, firstName: e.target.value })
                                }
                            />

                        </div>
                    </div>
                </div>

                <div className="h-[6.63rem] md:h-[5rem] w-full mt-[2.19rem] flex items-end border-t border-outline">
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                            Last name
                        </div>
                        <div className="flex-1">
                            <BareTextInput
                                placeholder="Enter name"
                                type="text"
                                value={formValues?.lastName}
                                onChange={(e) =>
                                    setFormValues({ ...formValues, lastName: e.target.value })
                                }
                            />

                        </div>
                    </div>
                </div>

                <div className="h-[6.63rem] md:h-[5rem] w-full flex items-end border-t border-outline">
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                            Role
                        </div>
                        <div className="flex-1">
                            <BareTextInput
                                placeholder="Enter your role"
                                type="text"
                                value={formValues?.role}
                                onChange={(e) =>
                                    setFormValues({ ...formValues, role: e.target.value })
                                }
                            />
                        </div>
                    </div>
                </div>

                <div className="h-[6.63rem] md:h-[5rem] w-full flex items-end border-t border-outline">
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                            Phone Number
                        </div>
                        <div className="flex-1">
                            <BareTextInput
                                placeholder="Enter your phone number"
                                type="text"
                                value={formValues?.phoneNumber}
                                onChange={(e) =>
                                    setFormValues({ ...formValues, phoneNumber: e.target.value })
                                }
                            />
                        </div>
                    </div>
                </div>

                <div className="w-full flex items-end border-t border-outline">
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem]">
                        <div className="w-full max-w-[9.13rem] pt-[2.56rem] font-medium lg:text-[1.125rem] text-subheading">
                            Profile image
                        </div>
                        <div className="flex gap-5 items-end">
                            <div className="relative mt-[0.81rem]">
                                {/* Profile Image Display */}
                                <div className="relative">
                                    {
                                        formValues?.profileImage ?
                                            <img
                                                className="w-[5.75rem] h-[5.75rem] object-cover rounded-full border-2 border-outline"
                                                src={formValues?.profileImage}
                                                alt="Profile"
                                            />
                                            :
                                            <MdAccountCircle className="w-[5.75rem] h-[5.75rem] text-headertext/90 rounded-full" />
                                    }

                                    {/* Add Image Button - Only show when no image */}
                                    {!formValues?.profileImage && (
                                        <div
                                            onClick={triggerFileInput}
                                            className="absolute cursor-pointer flex items-center justify-center w-[1.45rem] h-[1.45rem] bottom-[0.06rem] right-[0.06rem] bg-black rounded-full border-white border-[0.15rem] hover:bg-gray-800 transition-colors"
                                            title="Add profile image"
                                        >
                                            <img className="w-[0.94rem] h-[0.94rem]" src="/plus-small-white.svg" alt="Add" />
                                        </div>
                                    )}
                                </div>

                                {/* Action Buttons - Show when image is present */}
                                {formValues?.profileImage && (
                                    <div className="flex gap-2 mt-3">
                                        <button
                                            onClick={triggerFileInput}
                                            className="flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-black border border-black rounded-md hover:bg-gray-50 transition-colors"
                                            title="Change image"
                                        >
                                            Change
                                        </button>
                                        <button
                                            onClick={handleRemoveImage}
                                            className="flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-red-600 border border-red-600 rounded-md hover:bg-red-50 transition-colors"
                                            title="Remove image"
                                        >
                                            Remove
                                        </button>
                                    </div>
                                )}

                                <input
                                    type="file"
                                    ref={fileInputRef}
                                    accept="image/*"
                                    onChange={handleImageChange}
                                    className="hidden"
                                />
                            </div>
                            <div className="text-placeholder text-bold text-[0.8125rem]">
                                Aspect ratio 1 : 1. e.g. [200px x 200px]
                                <br />
                                <span className="text-xs text-subheading">Click to upload and crop your image</span>
                            </div>
                        </div>

                    </div>
                </div>

                <div className="w-full flex items-end border-t border-outline">
                    <div className="flex -lg:flex-col w-full gap-[0.5rem] lg:gap-[6.38rem]">
                        <div className="w-full max-w-[9.13rem] pt-[2.56rem] font-medium lg:text-[1.125rem] text-subheading">
                            Cover photo
                        </div>
                        <div onClick={() => { setIsModalOpen(true) }} className="relative lg:mt-[1.56rem] flex-1">
                            <img src={formValues?.coverPhoto} className="w-full object-cover h-[7rem] rounded-[0.625rem]" />
                            <div className="absolute cursor-pointer w-full h-full flex justify-center items-center top-0">
                                <div className="cursor-pointer flex items-center justify-center w-[2.38rem] h-[2.38rem] bg-black rounded-full">
                                    <FaImage className="w-[1.42rem] h-[1.42rem]" color="white" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="mt-[4.06rem]">
                <div className="font-semibold text-headertext lg:text-[1.125rem]">Links</div>

                <div className="md:h-[4.06rem] w-full mt-[2.19rem] flex items-end border-t border-outline">
                    <div className="flex w-full -md:justify-between gap-[0.5rem] md:gap-[6.38rem] items-start md:items-end">
                        <div className="flex w-full max-w-[9.13rem] -md:flex-col -md:gap-[0.75rem]">
                            <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                                Testimonials
                            </div>
                            <div
                                onClick={() => { router.push("/testimonials") }}
                                className="flex md:hidden cursor-pointer font-medium border-[0.055rem] border-black items-center bg-white text-black justify-center w-[11.31rem] h-[3.13rem] rounded-[0.55125rem]"
                            >
                                Set up testimonials
                            </div>
                        </div>
                        <AppToggleSwitch
                            on={formValues?.hasTestimonial}
                            onToggle={(value) =>
                                setFormValues({ ...formValues, hasTestimonial: value })
                            }
                            heightInRem={1.69}
                            widthInRem={2.94}
                        />
                    </div>
                </div>

                <div className="md:h-[4.06rem] w-full mt-[3.07rem] md:mt-[1.88rem] md:mt-[2.75rem] flex items-end border-t border-outline">
                    <div className="flex w-full -md:justify-between gap-[0.5rem] md:gap-[6.38rem] items-start md:items-end">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                            Social proofs
                        </div>
                        <AppToggleSwitch
                            on={formValues?.hasSocialProof}
                            onToggle={(value) =>
                                setFormValues({ ...formValues, hasSocialProof: value })
                            }
                            heightInRem={1.69}
                            widthInRem={2.94}
                        />
                    </div>
                </div>

                <div className="h-[6.63rem] md:h-[5rem] mt-[3.07rem] md:mt-[1.88rem] w-full flex items-end border-t border-outline">
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                            Website link
                        </div>
                        <div className="flex-1">
                            <BareTextInput
                                placeholder="paste link"
                                type="text"
                                value={formValues?.website}
                                onChange={(e) =>
                                    setFormValues({ ...formValues, website: e.target.value })
                                }
                            />
                        </div>
                    </div>
                </div>

                <div className="h-[6.63rem] md:h-[5rem] mt-[3.07rem] md:mt-[2.75rem] w-full flex items-end border-t border-outline">
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                            Location link
                        </div>
                        <div className="flex-1">
                            <BareTextInput
                                placeholder="paste link"
                                type="text"
                                value={formValues?.location}
                                onChange={(e) =>
                                    setFormValues({ ...formValues, location: e.target.value })
                                }
                            />
                        </div>
                    </div>
                </div>

                <div className="mt-[3.07rem] md:mt-[2.75rem] flex flex-col gap-[0.88rem]">
                    <div className="h-[6.63rem] md:h-[5rem] w-full flex items-end border-t border-outline">
                        <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                            <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                                Social profile
                            </div>
                            <div className="flex-1">
                                <SideLabelTextInput
                                    label="X"
                                    placeholder="https://x.com/yourcompany"
                                    type="text"
                                    value={formValues?.links.twitter}
                                    onChange={(e) =>
                                        setFormValues({
                                            ...formValues,
                                            links: { ...formValues?.links, twitter: e.target.value },
                                        })
                                    }
                                />
                            </div>
                        </div>
                    </div>
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                        </div>
                        <div className="flex-1">
                            <SideLabelTextInput
                                label="Instagram"
                                placeholder="https://instagram.com/yourcompany"
                                type="text"
                                value={formValues?.links.instagram}
                                onChange={(e) =>
                                    setFormValues({
                                        ...formValues,
                                        links: {
                                            ...formValues?.links,
                                            instagram: e.target.value,
                                        },
                                    })
                                }
                            />
                        </div>
                    </div>
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                        </div>
                        <div className="flex-1">
                            <SideLabelTextInput
                                label="LinkedIn"
                                placeholder="https://linkedin.com/in/yourprofile"
                                type="text"
                                value={formValues?.links.linkedin}
                                onChange={(e) =>
                                    setFormValues({
                                        ...formValues,
                                        links: {
                                            ...formValues?.links,
                                            linkedin: e.target.value,
                                        },
                                    })
                                }
                            />
                        </div>
                    </div>
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                        </div>
                        <div className="flex-1">
                            <SideLabelTextInput
                                label="Facebook"
                                placeholder="https://facebook.com/yourbusiness"
                                type="text"
                                value={formValues?.links.facebook}
                                onChange={(e) =>
                                    setFormValues({
                                        ...formValues,
                                        links: {
                                            ...formValues?.links,
                                            facebook: e.target.value,
                                        },
                                    })
                                }
                            />
                        </div>
                    </div>
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                        </div>
                        <div className="flex-1">
                            <SideLabelTextInput
                                label="TikTok"
                                placeholder="https://tiktok.com/@yourcompany"
                                type="text"
                                value={formValues?.links.tiktok}
                                onChange={(e) =>
                                    setFormValues({
                                        ...formValues,
                                        links: {
                                            ...formValues?.links,
                                            tiktok: e.target.value,
                                        },
                                    })
                                }
                            />
                        </div>
                    </div>
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                        </div>
                        <div className="flex-1">
                            <div className="relative">
                                <SideLabelTextInput
                                    label="WhatsApp"
                                    placeholder="+1234567890"
                                    type="text"
                                    hasError={!!whatsappError}
                                    value={formValues?.links.whatsapp?.startsWith('wa.me/') ? formValues.links.whatsapp.replace('wa.me/', '') : formValues?.links.whatsapp}
                                    onChange={(e) => {
                                        const phoneNumber = e.target.value;

                                        // Validate the input
                                        const error = validateWhatsAppNumber(phoneNumber);
                                        setWhatsappError(error);

                                        // Clean the phone number: remove spaces, dashes, and other formatting
                                        const cleanedNumber = phoneNumber.replace(/^wa\.me\//, '').replace(/[\s\-\(\)]/g, '');
                                        // Store with wa.me/ prefix if there's a value, otherwise store empty
                                        const whatsappValue = cleanedNumber.trim() ? `wa.me/${cleanedNumber}` : '';
                                        setFormValues({
                                            ...formValues,
                                            links: {
                                                ...formValues?.links,
                                                whatsapp: whatsappValue,
                                            },
                                        });
                                    }}
                                />
                                {whatsappError && (
                                    <div className="text-red-500 text-sm mt-1">{whatsappError}</div>
                                )}
                            </div>
                        </div>
                    </div>
                    <div className="flex w-full -md:flex-col gap-[0.5rem] md:gap-[6.38rem] md:items-center">
                        <div className="w-full max-w-[9.13rem] font-medium lg:text-[1.125rem] text-subheading">
                        </div>
                        <div className="flex-1">
                            <SideLabelTextInput
                                label="YouTube"
                                placeholder="https://youtube.com/@yourcompany"
                                type="text"
                                value={formValues?.links.youtube}
                                onChange={(e) =>
                                    setFormValues({
                                        ...formValues,
                                        links: {
                                            ...formValues?.links,
                                            youtube: e.target.value,
                                        },
                                    })
                                }
                            />
                        </div>
                    </div>
                </div>
            </div>

            <div className="flex justify-end gap-[0.69rem] mt-[4.06rem] pr-[1.75rem]">
                <div
                    onClick={() => { router.back() }}
                    className="flex cursor-pointer font-medium border-[0.055rem] border-black items-center bg-white text-black justify-center w-[6.81rem] h-[3.13rem] rounded-[0.55125rem]"
                >
                    Cancel
                </div>
                <div
                    onClick={() => {
                        // Validate WhatsApp before submission
                        const currentWhatsApp = formValues?.links.whatsapp?.startsWith('wa.me/')
                            ? formValues.links.whatsapp.replace('wa.me/', '')
                            : formValues?.links.whatsapp;
                        const error = validateWhatsAppNumber(currentWhatsApp || '');

                        if (error) {
                            setWhatsappError(error);
                            showNotification({
                                renderObj: { midSection: "Please fix the WhatsApp number format" },
                                type: "failure"
                            });
                            return;
                        }

                        updateProfile.mutate({ ...formValues, userId: parseInt(getUserId() as string) });
                    }}
                    className={` ${updateProfile?.isPending || whatsappError
                        ? "bg-placeholder border-placeholder text-white cursor-not-allowed"
                        : "bg-black text-white border-black cursor-pointer"} flex font-medium border-[0.055rem] items-center justify-center w-[11.81rem] h-[3.13rem] rounded-[0.55125rem]`}
                >
                    {
                        updateProfile?.isPending ? "Submitting" : "Save changes"
                    }
                </div>
            </div>
        </div>
    );
}
